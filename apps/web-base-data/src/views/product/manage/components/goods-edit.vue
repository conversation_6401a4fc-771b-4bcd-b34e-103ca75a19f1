<script setup lang="ts">
import { nextTick, reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import { AutoComplete, Button, Card, Col, Form, FormItem, Input, Row, Select } from 'ant-design-vue';

import { getAttributeUnitList, getSpecificationListApi, getTaxonomyListApi } from '#/api';

const colSpan = { md: 12, sm: 24 };
const init = (data) => {};
const save = async () => {};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const formRef = ref();
const goodsForm = ref({
  newSkuList: [],
});
const rules = {};
const addSku = () => {
  goodsForm.value.newSkuList.push({ skuName: '' });
};
const delSku = (index) => {
  goodsForm.value.newSkuList.splice(index, 1);
};
const specList = ref([]);
const getSpecList = async () => {
  const res = await getSpecificationListApi();
  specList.value = res.map((item) => {
    return { label: item.specName, value: item.specName, specValueJson: item.specValueJson };
  });
};
getSpecList();
const selectSpec = (value, option, skuItem) => {
  skuItem.specValueList = option.specValueJson ? JSON.parse(option.specValueJson) : [];
};
const state = reactive({
  inputVisible: false,
  inputValue: '',
});
const inputRef = ref();
const handleDeleteSpecValue = (tag, list) => {
  list.splice(list.indexOf(tag), 1);
};
const showInput = () => {
  state.inputVisible = true;
};

const handleInputConfirm = (skuItem) => {
  const inputValue = state.inputValue;
  let tags = skuItem.specValueList ?? [];
  if (inputValue && !tags.includes(inputValue)) {
    tags = [...tags, inputValue];
  }
  skuItem.specValueList = tags;
  state.inputValue = '';
  state.inputVisible = false;
};
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="商品信息" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="goodsForm"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="商品编码" name="spuCode">
            <Input v-model:value="goodsForm.spuCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="商品名称" name="spuName">
            <Input v-model:value="goodsForm.spuName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="商品分类" name="categoryId">
            <ApiComponent
              v-model="goodsForm.categoryId"
              :component="Select"
              :api="getTaxonomyListApi"
              label-field="categoryName"
              value-field="pid"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="规格型号" name="specificationModel">
            <Input v-model:value="goodsForm.specificationModel" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="计量单位" name="measureUnit">
            <ApiComponent
              v-model="goodsForm.measureUnit"
              :component="Select"
              :api="getAttributeUnitList"
              label-field="unitName"
              value-field="pid"
            />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="商品规格" />
      <Row class="mt-5">
        <Col :span="24">
          <Card v-for="(item, index) in goodsForm.newSkuList" :key="index">
            <template #title>
              <div class="w-[400px]">
                <FormItem label="规格名" class="mb-0">
                  <AutoComplete
                    v-model:value="item.skuName"
                    :options="specList"
                    @select="(value, option) => selectSpec(value, option, item)"
                  />
                </FormItem>
              </div>
            </template>
            <template #extra>
              <Button type="link" @click="delSku(index)">删除</Button>
            </template>
            <a-tag
              v-for="(tag, index) in item.specValueList"
              :key="tag + index"
              closable
              color="blue"
              @close="handleDeleteSpecValue(tag, item.specValueList)"
            >
              {{ tag }}
            </a-tag>
            <a-input
              v-if="state.inputVisible"
              ref="inputRef"
              v-model:value="state.inputValue"
              type="text"
              size="small"
              :style="{ width: '78px' }"
              @blur="handleInputConfirm(item)"
              @keyup.enter="handleInputConfirm(item)"
            />
            <a-tag v-else class="cursor-pointer" @click="showInput">
              <PlusOutlined />
              添加
            </a-tag>
          </Card>
        </Col>
        <Col :span="24">
          <div class="mt-2">
            <Button type="primary" @click="addSku">添加商品规格</Button>
          </div>
        </Col>
      </Row>
    </Form>
  </BasicPopup>
</template>

<style scoped></style>
